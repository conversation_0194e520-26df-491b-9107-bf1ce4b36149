{"is_obsidian_vault": true, "smart_blocks": {"embed_blocks": true, "min_chars": 200}, "smart_sources": {"single_file_data_path": ".smart-env/smart_sources.json", "min_chars": 200, "embed_model": {"adapter": "transformers", "transformers": {"legacy_transformers": false, "model_key": "TaylorAI/bge-micro-v2"}, "TaylorAI/bge-micro-v2": {}}}, "file_exclusions": "Untitled", "folder_exclusions": "", "smart_view_filter": {"render_markdown": true, "show_full_path": false, "expanded_view": false, "results_limit": "20"}, "excluded_headings": "", "language": "en", "new_user": true, "smart_chat_threads": {"chat_model": {"adapter": "gemini", "ollama": {"model_key": "undefined"}, "azure": {"model_key": "", "azure_resource_name": "", "azure_deployment_name": "", "azure_api_version": "2024-10-01-preview"}, "gemini": {"model_key": ""}, "anthropic": {"model_key": "claude-3-haiku-20240307"}, "lm_studio": {"model_key": ""}, "custom": {"api_adapter": "gemini"}}, "active_thread_key": "Untitled Chat 1750128290286", "system_prompt": "", "detect_self_referential": true, "review_context": true, "stream": true, "language": "zh", "modifier_key_to_send": "shift", "use_tool_calls": true}, "smart_notices": {"muted": {}}, "version": ""}