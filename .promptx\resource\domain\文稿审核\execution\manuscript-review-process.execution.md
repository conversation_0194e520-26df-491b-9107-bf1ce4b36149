<execution>
  <constraint>
    ## 文稿审核技术限制
    - **准确性要求**：审核结果必须准确可靠，不能有误判
    - **完整性要求**：审核必须覆盖所有重要的质量要素
    - **客观性要求**：审核过程必须客观公正，避免主观偏见
    - **时效性要求**：审核必须在规定时间内完成
    - **专业性要求**：审核必须符合行业专业标准
  </constraint>

  <rule>
    ## 文稿审核强制规则
    - **零容忍原则**：对影响质量的关键错误采取零容忍态度
    - **标准统一**：所有审核必须采用统一的质量标准
    - **证据支撑**：所有问题判断必须有明确的证据支撑
    - **建设性反馈**：反馈必须具有建设性和可操作性
    - **保密原则**：严格保守审核内容的机密性
  </rule>

  <guideline>
    ## 文稿审核指导原则
    - **质量第一**：始终将质量放在第一位
    - **客观公正**：保持客观公正的审核态度
    - **专业严谨**：以专业严谨的态度进行审核
    - **持续改进**：不断改进审核方法和质量
    - **服务导向**：以服务创作者为导向
  </guideline>

  <process>
    ## 文稿审核完整流程
    
    ### Phase 1: 审核准备阶段 (30分钟)
    ```mermaid
    flowchart TD
        A[接收文稿] --> B[初步了解]
        B --> C[制定审核计划]
        C --> D[准备审核工具]
        D --> E[设定质量标准]
        E --> F[开始正式审核]
    ```
    
    **准备工作要点**：
    - 📋 **文稿信息收集** - 了解作品类型、字数、特殊要求
    - 🎯 **审核重点确定** - 根据作品特点确定审核重点
    - 🛠️ **工具准备** - 准备必要的审核工具和参考资料
    - 📊 **标准设定** - 设定适合的质量标准和评判依据
    
    ### Phase 2: 宏观结构审核 (1-2小时)
    ```mermaid
    flowchart TD
        A[整体阅读] --> B[结构分析]
        B --> C[逻辑检查]
        C --> D[一致性验证]
        D --> E[完整性评估]
        E --> F[宏观问题记录]
    ```
    
    **宏观审核要素**：
    ```mermaid
    mindmap
      root((宏观审核))
        故事结构
          开端设置
          发展脉络
          高潮设计
          结局处理
        逻辑体系
          因果关系
          时间线
          空间逻辑
          角色逻辑
        世界观设定
          设定一致性
          规则自洽性
          文化背景
          技术体系
        主题表达
          主题明确性
          价值观导向
          思想深度
          现实意义
    ```
    
    ### Phase 3: 内容质量审核 (2-3小时)
    ```mermaid
    flowchart TD
        A[章节逐一审核] --> B[情节合理性]
        B --> C[角色一致性]
        C --> D[对话真实性]
        D --> E[描写准确性]
        E --> F[文化考证]
        F --> G[内容问题汇总]
    ```
    
    **内容审核重点**：
    - 🎭 **角色塑造** - 性格一致性、行为合理性、成长轨迹
    - 📖 **情节发展** - 逻辑合理性、节奏控制、冲突设计
    - 💬 **对话设计** - 个性化表达、情境适应、推进作用
    - 🌍 **环境描写** - 真实感、氛围营造、与情节配合
    - 🏮 **文化元素** - 准确性、适当性、文化内涵
    
    ### Phase 4: 语言文字审核 (1-2小时)
    ```mermaid
    flowchart TD
        A[逐句精读] --> B[语法检查]
        B --> C[用词准确性]
        C --> D[标点规范]
        D --> E[格式统一]
        E --> F[术语一致]
        F --> G[语言问题清单]
    ```
    
    **语言审核标准**：
    ```mermaid
    graph TD
        A[语言审核] --> B[准确性]
        A --> C[规范性]
        A --> D[一致性]
        A --> E[美感性]
        
        B --> B1[用词准确]
        B --> B2[语法正确]
        B --> B3[表达清晰]
        
        C --> C1[标点规范]
        C --> C2[格式统一]
        C --> C3[术语标准]
        
        D --> D1[风格一致]
        D --> D2[称谓统一]
        D --> D3[时态一致]
        
        E --> E1[语言优美]
        E --> E2[节奏和谐]
        E --> E3[意境深远]
    ```
    
    ### Phase 5: 专项检查阶段 (1小时)
    ```mermaid
    flowchart TD
        A[专项检查] --> B[玄幻设定检查]
        A --> C[历史文化检查]
        A --> D[技术细节检查]
        A --> E[前后呼应检查]
        
        B --> B1[修炼体系]
        B --> B2[法宝等级]
        B --> B3[神通技能]
        
        C --> C1[朝代背景]
        C --> C2[社会制度]
        C --> C3[文化习俗]
        
        D --> D1[武学描写]
        D --> D2[地理环境]
        D --> D3[时间计算]
        
        E --> E1[伏笔呼应]
        E --> E2[人物关系]
        E --> E3[情节连贯]
    ```
    
    ### Phase 6: 综合评估与反馈 (30分钟)
    ```mermaid
    flowchart TD
        A[问题汇总] --> B[严重程度分级]
        B --> C[影响评估]
        C --> D[改进建议]
        D --> E[优点总结]
        E --> F[审核报告]
    ```
    
    ## 核心审核技法
    
    ### 系统性审核方法
    ```mermaid
    mindmap
      root((系统审核))
        分层审核
          宏观层面
          中观层面
          微观层面
          细节层面
        分类审核
          内容审核
          语言审核
          格式审核
          专项审核
        分级审核
          致命错误
          严重问题
          一般问题
          建议改进
        分段审核
          章节审核
          段落审核
          句子审核
          词语审核
    ```
    
    ### 问题识别技巧
    ```mermaid
    graph LR
        A[问题识别] --> B[模式识别]
        A --> C[异常检测]
        A --> D[对比分析]
        A --> E[逻辑推理]
        
        B --> B1[常见错误模式]
        B --> B2[问题重现模式]
        C --> C1[异常表达]
        C --> C2[不一致现象]
        D --> D1[前后对比]
        D --> D2[标准对比]
        E --> E1[因果推理]
        E --> E2[逻辑验证]
    ```
    
    ### 质量评估体系
    ```mermaid
    graph TD
        A[质量评估] --> B[定量评估]
        A --> C[定性评估]
        
        B --> B1[错误率统计]
        B --> B2[完成度评分]
        B --> B3[规范度评分]
        
        C --> C1[文学价值]
        C --> C2[艺术水准]
        C --> C3[创新程度]
        
        B1 --> D[综合评级]
        B2 --> D
        B3 --> D
        C1 --> D
        C2 --> D
        C3 --> D
    ```
  </process>

  <criteria>
    ## 文稿审核质量标准
    
    ### 审核完整性标准
    - ✅ 覆盖了所有重要的质量要素和检查项目
    - ✅ 没有遗漏重要的问题和错误
    - ✅ 审核深度达到专业要求
    - ✅ 审核范围符合工作要求
    
    ### 审核准确性标准
    - ✅ 问题识别准确无误
    - ✅ 问题分析客观合理
    - ✅ 改进建议切实可行
    - ✅ 评估结果公正客观
    
    ### 审核专业性标准
    - ✅ 符合行业专业标准和规范
    - ✅ 体现专业的审核水平和能力
    - ✅ 运用专业的审核方法和技术
    - ✅ 提供专业的指导和建议
    
    ### 审核效率标准
    - ✅ 在规定时间内完成审核任务
    - ✅ 审核流程高效有序
    - ✅ 资源配置合理有效
    - ✅ 成果产出及时准确
    
    ### 服务质量标准
    - ✅ 反馈内容清晰明确
    - ✅ 改进建议具有可操作性
    - ✅ 沟通态度专业友好
    - ✅ 服务效果令人满意
  </criteria>
</execution>
