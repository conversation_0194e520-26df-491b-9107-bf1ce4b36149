<execution>
  <constraint>
    ## 内容质量控制技术限制
    - **客观性要求**：质量评判必须基于客观标准，避免主观偏见
    - **全面性要求**：质量控制必须覆盖内容的所有重要方面
    - **一致性要求**：质量标准的应用必须前后一致
    - **可操作性要求**：质量改进建议必须具有可操作性
    - **专业性要求**：质量控制必须符合专业标准和规范
  </constraint>

  <rule>
    ## 内容质量控制强制规则
    - **标准优先**：始终以既定的质量标准为准绳
    - **证据导向**：所有质量判断必须有充分的证据支撑
    - **系统性原则**：采用系统性的方法进行质量控制
    - **持续改进**：建立持续改进的质量控制机制
    - **责任明确**：明确质量控制的责任和权限
  </rule>

  <guideline>
    ## 内容质量控制指导原则
    - **预防为主**：以预防质量问题为主要导向
    - **过程控制**：在整个过程中实施质量控制
    - **数据驱动**：基于数据和事实进行质量决策
    - **协作配合**：与相关人员协作配合提升质量
    - **用户导向**：以最终用户的需求为导向
  </guideline>

  <process>
    ## 内容质量控制完整流程
    
    ### Phase 1: 质量标准设定 (30分钟)
    ```mermaid
    flowchart TD
        A[需求分析] --> B[标准制定]
        B --> C[指标设定]
        C --> D[基准确定]
        D --> E[标准发布]
        E --> F[标准培训]
    ```
    
    **质量标准体系**：
    ```mermaid
    mindmap
      root((质量标准))
        内容标准
          逻辑性标准
          完整性标准
          准确性标准
          创新性标准
        文学标准
          语言美感
          艺术价值
          思想深度
          文化内涵
        技术标准
          格式规范
          错误率控制
          一致性要求
          可读性标准
        用户标准
          满意度要求
          体验质量
          价值认知
          推荐意愿
    ```
    
    ### Phase 2: 质量检查执行 (2-3小时)
    ```mermaid
    flowchart TD
        A[检查计划] --> B[逐项检查]
        B --> C[问题记录]
        C --> D[证据收集]
        D --> E[影响评估]
        E --> F[检查报告]
    ```
    
    **质量检查要点**：
    - 📖 **内容逻辑性** - 情节发展的逻辑合理性
    - 🎭 **角色一致性** - 人物性格和行为的一致性
    - 🌍 **世界观自洽** - 设定体系的内在一致性
    - 📚 **文化准确性** - 文化背景和细节的准确性
    - 💎 **语言质量** - 文字表达的准确性和美感
    
    ### Phase 3: 问题分析诊断 (1小时)
    ```mermaid
    flowchart TD
        A[问题汇总] --> B[分类整理]
        B --> C[原因分析]
        C --> D[影响评估]
        D --> E[优先级排序]
        E --> F[解决方案]
    ```
    
    **问题分析维度**：
    ```mermaid
    graph TD
        A[问题分析] --> B[严重程度]
        A --> C[影响范围]
        A --> D[解决难度]
        A --> E[紧急程度]
        
        B --> B1[致命问题]
        B --> B2[严重问题]
        B --> B3[一般问题]
        B --> B4[轻微问题]
        
        C --> C1[全局影响]
        C --> C2[局部影响]
        C --> C3[单点影响]
        
        D --> D1[容易解决]
        D --> D2[中等难度]
        D --> D3[困难解决]
        
        E --> E1[立即处理]
        E --> E2[优先处理]
        E --> E3[计划处理]
    ```
    
    ### Phase 4: 改进措施制定 (30分钟)
    ```mermaid
    flowchart TD
        A[改进目标] --> B[措施设计]
        B --> C[资源配置]
        C --> D[时间安排]
        D --> E[责任分工]
        E --> F[实施计划]
    ```
    
    ### Phase 5: 质量验证确认 (30分钟)
    ```mermaid
    flowchart TD
        A[改进实施] --> B[效果检查]
        B --> C[质量验证]
        C --> D[标准对比]
        D --> E[结果确认]
        E --> F[质量报告]
    ```
    
    ## 专项质量控制技法
    
    ### 玄幻小说专项控制
    ```mermaid
    mindmap
      root((玄幻专项))
        修炼体系
          境界划分
          突破逻辑
          能力对应
          成长合理性
        法宝系统
          等级体系
          功能设定
          获得方式
          使用限制
        世界观设定
          地理环境
          社会结构
          历史背景
          文化传统
        战斗描写
          招式设计
          威力表现
          战术运用
          结果合理
    ```
    
    ### 文化背景质控
    ```mermaid
    graph LR
        A[文化质控] --> B[历史考证]
        A --> C[社会制度]
        A --> D[风俗习惯]
        A --> E[语言表达]
        
        B --> B1[时代背景]
        B --> B2[重大事件]
        B --> B3[历史人物]
        
        C --> C1[政治制度]
        C --> C2[经济体系]
        C --> C3[社会阶层]
        
        D --> D1[节庆习俗]
        D --> D2[生活方式]
        D --> D3[宗教信仰]
        
        E --> E1[古典用词]
        E --> E2[称谓系统]
        E --> E3[表达方式]
    ```
    
    ### 逻辑一致性控制
    ```mermaid
    flowchart LR
        A[逻辑控制] --> B[时间逻辑]
        A --> C[空间逻辑]
        A --> D[因果逻辑]
        A --> E[角色逻辑]
        
        B --> B1[时间线一致]
        B --> B2[年龄增长]
        B --> B3[事件顺序]
        
        C --> C1[地理位置]
        C --> C2[距离关系]
        C --> C3[环境描述]
        
        D --> D1[原因结果]
        D --> D2[动机行为]
        D --> D3[条件结果]
        
        E --> E1[性格一致]
        E --> E2[能力匹配]
        E --> E3[成长轨迹]
    ```
  </process>

  <criteria>
    ## 内容质量控制标准
    
    ### 逻辑性标准
    - ✅ 情节发展逻辑清晰合理
    - ✅ 因果关系明确可信
    - ✅ 时间空间逻辑一致
    - ✅ 角色行为逻辑自洽
    
    ### 一致性标准
    - ✅ 世界观设定前后一致
    - ✅ 角色性格行为一致
    - ✅ 术语使用统一规范
    - ✅ 风格表达协调统一
    
    ### 准确性标准
    - ✅ 文化背景考证准确
    - ✅ 历史细节真实可信
    - ✅ 专业知识运用正确
    - ✅ 语言表达准确规范
    
    ### 完整性标准
    - ✅ 故事结构完整清晰
    - ✅ 情节发展完整连贯
    - ✅ 角色塑造完整立体
    - ✅ 主题表达完整深刻
    
    ### 创新性标准
    - ✅ 具有独特的创意和想法
    - ✅ 在传统基础上有所创新
    - ✅ 表达方式新颖有趣
    - ✅ 给读者带来新鲜体验
  </criteria>
</execution>
