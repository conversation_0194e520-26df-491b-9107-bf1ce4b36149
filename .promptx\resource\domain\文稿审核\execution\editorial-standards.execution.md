<execution>
  <constraint>
    ## 编辑标准技术限制
    - **规范性要求**：必须严格遵循行业编辑标准和规范
    - **一致性要求**：编辑标准的应用必须前后一致
    - **专业性要求**：编辑工作必须体现专业水准
    - **准确性要求**：编辑判断必须准确可靠
    - **效率性要求**：编辑工作必须高效完成
  </constraint>

  <rule>
    ## 编辑标准强制规则
    - **标准统一**：所有编辑工作必须采用统一标准
    - **质量优先**：质量要求高于速度要求
    - **证据支撑**：所有编辑决定必须有明确依据
    - **专业操守**：严格遵守编辑职业道德和操守
    - **保密原则**：严格保守稿件内容的机密性
  </rule>

  <guideline>
    ## 编辑标准指导原则
    - **服务作者**：以服务作者创作为根本目标
    - **提升质量**：致力于提升作品的整体质量
    - **尊重原创**：尊重作者的原创性和个人风格
    - **建设性反馈**：提供建设性的修改意见和建议
    - **持续学习**：不断学习和更新编辑知识技能
  </guideline>

  <process>
    ## 编辑标准执行流程
    
    ### Phase 1: 编辑准备阶段 (30分钟)
    ```mermaid
    flowchart TD
        A[稿件接收] --> B[初步评估]
        B --> C[编辑计划]
        C --> D[标准设定]
        D --> E[工具准备]
        E --> F[开始编辑]
    ```
    
    **编辑准备要点**：
    - 📋 **稿件信息** - 了解作品类型、风格、目标读者
    - 🎯 **编辑目标** - 明确编辑的具体目标和要求
    - 📊 **标准选择** - 选择适合的编辑标准和规范
    - 🛠️ **工具配置** - 准备必要的编辑工具和参考资料
    
    ### Phase 2: 结构编辑阶段 (2-3小时)
    ```mermaid
    flowchart TD
        A[整体结构] --> B[章节安排]
        B --> C[情节逻辑]
        C --> D[角色发展]
        D --> E[主题表达]
        E --> F[结构优化]
    ```
    
    **结构编辑标准**：
    ```mermaid
    mindmap
      root((结构编辑))
        故事架构
          开端设计
          发展脉络
          高潮安排
          结局处理
        章节组织
          章节划分
          长度控制
          过渡衔接
          节奏把握
        情节安排
          主线清晰
          副线合理
          冲突设计
          悬念营造
        角色塑造
          主角突出
          配角丰满
          关系清晰
          成长轨迹
    ```
    
    ### Phase 3: 内容编辑阶段 (3-4小时)
    ```mermaid
    flowchart TD
        A[内容审核] --> B[逻辑检查]
        B --> C[事实核实]
        C --> D[文化考证]
        D --> E[风格统一]
        E --> F[内容优化]
    ```
    
    **内容编辑重点**：
    - 🔍 **逻辑性检查** - 情节逻辑、因果关系、时空逻辑
    - 📚 **事实准确性** - 历史事实、文化背景、专业知识
    - 🎭 **角色一致性** - 性格特征、行为模式、语言风格
    - 🌍 **世界观统一** - 设定规则、体系完整、内在逻辑
    - 💎 **文学价值** - 思想深度、艺术水准、创新程度
    
    ### Phase 4: 语言编辑阶段 (2-3小时)
    ```mermaid
    flowchart TD
        A[语言风格] --> B[表达优化]
        B --> C[用词精准]
        C --> D[句式调整]
        D --> E[节奏控制]
        E --> F[语言润色]
    ```
    
    **语言编辑标准**：
    ```mermaid
    graph TD
        A[语言编辑] --> B[准确性]
        A --> C[流畅性]
        A --> D[美感性]
        A --> E[一致性]
        
        B --> B1[用词准确]
        B --> B2[语法正确]
        B --> B3[表达清晰]
        
        C --> C1[句式流畅]
        C --> C2[逻辑连贯]
        C --> C3[阅读顺畅]
        
        D --> D1[语言优美]
        D --> D2[意境深远]
        D --> D3[韵律和谐]
        
        E --> E1[风格统一]
        E --> E2[术语一致]
        E --> E3[格调协调]
    ```
    
    ### Phase 5: 技术编辑阶段 (1-2小时)
    ```mermaid
    flowchart TD
        A[格式规范] --> B[标点符号]
        B --> C[错别字检查]
        C --> D[术语统一]
        D --> E[版式调整]
        E --> F[最终校对]
    ```
    
    **技术编辑要求**：
    - ✏️ **文字校对** - 错别字、标点符号、语法错误
    - 📐 **格式规范** - 段落格式、对话格式、章节标题
    - 🔤 **术语统一** - 人名地名、专业术语、技能名称
    - 📊 **版式调整** - 排版格式、字体字号、行间距
    
    ### Phase 6: 质量验收阶段 (30分钟)
    ```mermaid
    flowchart TD
        A[编辑完成] --> B[质量检查]
        B --> C[标准对照]
        C --> D[问题确认]
        D --> E[最终审核]
        E --> F[编辑报告]
    ```
    
    ## 专业编辑技法
    
    ### 玄幻小说编辑特色
    ```mermaid
    mindmap
      root((玄幻编辑))
        世界观编辑
          设定完善
          规则统一
          逻辑自洽
          创新合理
        修炼体系
          境界清晰
          进阶合理
          能力匹配
          描写生动
        战斗场面
          招式设计
          威力表现
          节奏控制
          结果合理
        文化融入
          传统文化
          现代表达
          文化准确
          创新发展
    ```
    
    ### 文学性提升技法
    ```mermaid
    graph LR
        A[文学性提升] --> B[语言美化]
        A --> C[意境营造]
        A --> D[情感深化]
        A --> E[思想升华]
        
        B --> B1[修辞运用]
        B --> B2[词汇丰富]
        B --> B3[句式变化]
        
        C --> C1[环境描写]
        C --> C2[氛围营造]
        C --> C3[意象运用]
        
        D --> D1[情感真实]
        D --> D2[层次丰富]
        D --> D3[共鸣强烈]
        
        E --> E1[主题深刻]
        E --> E2[价值积极]
        E --> E3[启发思考]
    ```
    
    ### 可读性优化方法
    ```mermaid
    flowchart LR
        A[可读性优化] --> B[结构优化]
        A --> C[语言简化]
        A --> D[节奏调整]
        A --> E[信息组织]
        
        B --> B1[逻辑清晰]
        B --> B2[层次分明]
        B --> B3[过渡自然]
        
        C --> C1[表达简洁]
        C --> C2[用词准确]
        C --> C3[句式多样]
        
        D --> D1[快慢相间]
        D --> D2[张弛有度]
        D --> D3[高潮突出]
        
        E --> E1[信息分层]
        E --> E2[重点突出]
        E --> E3[易于理解]
    ```
  </process>

  <criteria>
    ## 编辑标准质量要求
    
    ### 结构编辑标准
    - ✅ 故事结构完整清晰，逻辑合理
    - ✅ 章节安排恰当，节奏控制得当
    - ✅ 情节发展自然，冲突设计有力
    - ✅ 角色塑造立体，成长轨迹清晰
    
    ### 内容编辑标准
    - ✅ 内容逻辑严密，事实准确可靠
    - ✅ 文化背景考证准确，表达恰当
    - ✅ 世界观设定统一，内在逻辑自洽
    - ✅ 主题表达深刻，价值导向积极
    
    ### 语言编辑标准
    - ✅ 语言表达准确流畅，风格统一
    - ✅ 用词精准恰当，句式富有变化
    - ✅ 修辞运用得当，文学美感突出
    - ✅ 节奏控制合理，阅读体验良好
    
    ### 技术编辑标准
    - ✅ 文字准确无误，标点符号规范
    - ✅ 格式统一规范，版式美观整洁
    - ✅ 术语使用一致，专业表达准确
    - ✅ 技术细节完善，质量达到要求
    
    ### 整体质量标准
    - ✅ 编辑质量达到专业出版标准
    - ✅ 作品整体质量显著提升
    - ✅ 保持作者原有风格和特色
    - ✅ 读者阅读体验明显改善
  </criteria>
</execution>
