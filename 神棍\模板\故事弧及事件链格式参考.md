# 故事弧：标题

## 前情提要


## 基础设定
*基本的故事设定，设计参考，请勿修改*

## 故事情节摘要

**故事弧完整的情节摘要**：

**故事弧完整的因果链条**：

## 故事弧整体设计规划

#### 主线发展脉络

- **时间节点**：
- **设计目的**：
- **核心目标**：

- **首次登场**：（*如果人物库中没有合适本章内容的角色，需要新增角色，你可以直接使用"新增角色A"来表示该人物，并写出该角色的必要属性，我会及时新建设计对应角色来替换这个"新增角色"*）
- **出场人物**：
- **主要舞台**：

- **关键行动**：
- **受到阻碍**：
- **矛盾冲突**：

- **主要事件与转折点**：
- **重要对白**：

- **不在本故事弧内容但对白提及**：
- **主要情节线索与伏笔安排**：（当前剧情发生的线索与对应的伏笔）
- **是否计划长线伏笔与回收**：
- **是否设计悬念和情节钩子**：
- **是否安排剧情反转**：

#### 支线发展脉络

#### 暗线发展脉络

#### 情感线发展脉络

#### 后续期待构想

## 必要的前置条件伏笔与铺垫：
（这里写的是完成本次剧情需要的前置条件，哪些剧情需要前期做铺垫或埋下伏笔）

## 关键信息与设定缺失补全
（当剧情设计时缺少重要的关键信息时，在这里设计补充信息设定，让故事逻辑自洽）

## 第一幕：标题

- **主要角色**：
- **核心动机**：
- **舞台构建**：
- **关键行动**：
1. 
- **矛盾冲突**：
- **赌注代价**：
- **设计目的**：
1. 
2. 
3. 
4. 
---
#### 事件一：标题
- **场景搭建**：
- **情节摘要**：
- **设计目的**：
1. 

#### 事件n：标题
- **场景搭建**：
- **情节摘要**：
- **设计目的**：
1. 

#### 幕尾关键转折与情节钩子

## 故事弧总结
