<execution>
  <constraint>
    ## 情感评估技术限制
    - **真实性要求**：情感评估必须基于真实的情感体验
    - **客观性要求**：避免过度主观化影响评估结果
    - **全面性要求**：评估要覆盖情感体验的各个重要方面
    - **准确性要求**：情感强度和质量的评估要准确可信
    - **可比性要求**：评估结果要具有可比较的标准和依据
  </constraint>

  <rule>
    ## 情感评估强制规则
    - **体验优先**：以真实的情感体验为评估的根本依据
    - **标准统一**：采用统一的情感评估标准和方法
    - **证据支撑**：所有情感评估必须有具体的文本证据
    - **层次分析**：从多个层次分析情感的复杂性和丰富性
    - **读者导向**：以读者的情感需求和体验为评估导向
  </rule>

  <guideline>
    ## 情感评估指导原则
    - **感同身受**：以真诚的共情态度进行情感评估
    - **细致入微**：关注情感体验的细微变化和差异
    - **动态跟踪**：跟踪情感在阅读过程中的动态变化
    - **价值判断**：在情感评估中体现积极的价值判断
    - **建设性反馈**：提供有助于情感表达改进的建议
  </guideline>

  <process>
    ## 情感评估完整流程
    
    ### Phase 1: 情感体验记录 (阅读过程中)
    ```mermaid
    flowchart TD
        A[开始阅读] --> B[情感反应记录]
        B --> C[情感强度标记]
        C --> D[情感变化追踪]
        D --> E[触发点识别]
        E --> F[持续时间记录]
    ```
    
    **情感记录要点**：
    - 💓 **即时反应** - 阅读时的即时情感反应和感受
    - 📊 **强度变化** - 情感强度的起伏变化过程
    - 🎯 **触发因素** - 引发特定情感的具体内容要素
    - ⏱️ **持续效果** - 情感影响的持续时间和深度
    
    ### Phase 2: 情感类型分析 (30分钟)
    ```mermaid
    flowchart TD
        A[情感数据整理] --> B[基础情感识别]
        B --> C[复合情感分析]
        C --> D[社会情感评估]
        D --> E[精神情感探讨]
        E --> F[情感类型总结]
    ```
    
    **情感分类体系**：
    ```mermaid
    mindmap
      root((情感类型))
        基础情感
          喜悦快乐
          愤怒不满
          悲伤难过
          恐惧紧张
          惊讶意外
          厌恶反感
        复合情感
          感动震撼
          温暖治愈
          紧张刺激
          失落遗憾
          期待希望
          满足成就
        社会情感
          友情温暖
          爱情甜蜜
          亲情深沉
          师恩感激
          同情怜悯
          敬佩崇拜
        精神情感
          理想追求
          信念坚定
          价值认同
          精神升华
          人生感悟
          哲理思辨
    ```
    
    ### Phase 3: 情感强度评估 (30分钟)
    ```mermaid
    flowchart TD
        A[情感强度分析] --> B[瞬间冲击力]
        B --> C[持续影响力]
        C --> D[深度触动力]
        D --> E[记忆保持力]
        E --> F[行为影响力]
        F --> G[强度综合评分]
    ```
    
    **强度评估维度**：
    ```mermaid
    graph TD
        A[强度评估] --> B[冲击强度]
        A --> C[持续强度]
        A --> D[深度强度]
        A --> E[广度强度]
        
        B --> B1[瞬间震撼]
        B --> B2[情感爆发]
        B --> B3[心理冲击]
        
        C --> C1[影响时长]
        C --> C2[余韵效果]
        C --> C3[回味价值]
        
        D --> D1[触动深度]
        D --> D2[共鸣程度]
        D --> D3[内心震动]
        
        E --> E1[影响范围]
        E --> E2[传播效应]
        E --> E3[社会共鸣]
    ```
    
    ### Phase 4: 情感真实性评估 (30分钟)
    ```mermaid
    flowchart TD
        A[真实性评估] --> B[情感表达自然性]
        B --> C[情感逻辑合理性]
        C --> D[情感层次丰富性]
        D --> E[情感发展连贯性]
        E --> F[真实性综合判断]
    ```
    
    **真实性评估标准**：
    - 🎭 **表达自然性** - 情感表达是否自然流畅，不做作
    - 🧠 **逻辑合理性** - 情感产生和发展是否符合逻辑
    - 🌈 **层次丰富性** - 情感是否具有丰富的层次和变化
    - 🔗 **发展连贯性** - 情感发展是否连贯一致
    
    ### Phase 5: 情感共鸣分析 (45分钟)
    ```mermaid
    flowchart TD
        A[共鸣分析] --> B[个人共鸣评估]
        B --> C[群体共鸣预测]
        C --> D[文化共鸣分析]
        D --> E[时代共鸣判断]
        E --> F[共鸣效果综合]
    ```
    
    **共鸣层次分析**：
    ```mermaid
    graph LR
        A[共鸣层次] --> B[表层共鸣]
        A --> C[深层共鸣]
        A --> D[文化共鸣]
        A --> E[普世共鸣]
        
        B --> B1[情节共鸣]
        B --> B2[角色共鸣]
        C --> C1[价值共鸣]
        C --> C2[精神共鸣]
        D --> D1[传统文化]
        D --> D2[民族情感]
        E --> E1[人类共性]
        E --> E2[普遍情感]
    ```
    
    ### Phase 6: 情感效果评估 (30分钟)
    ```mermaid
    flowchart TD
        A[效果评估] --> B[即时效果]
        B --> C[短期效果]
        C --> D[长期效果]
        D --> E[行为影响]
        E --> F[价值实现]
        F --> G[效果综合评价]
    ```
    
    ## 专业评估技法
    
    ### 情感强度量化方法
    ```mermaid
    mindmap
      root((强度量化))
        量化标准
          1-2分轻微
          3-4分一般
          5-6分明显
          7-8分强烈
          9-10分极强
        测量维度
          冲击力强度
          持续时间
          影响深度
          记忆程度
        评估方法
          主观感受
          生理反应
          行为表现
          后续影响
        参照对比
          同类作品
          经典作品
          个人经历
          群体标准
    ```
    
    ### 情感质量评估法
    ```mermaid
    graph TD
        A[质量评估] --> B[情感纯度]
        A --> C[情感复杂度]
        A --> D[情感创新度]
        A --> E[情感感染力]
        
        B --> B1[情感单一性]
        B --> B2[情感集中度]
        C --> C1[层次丰富]
        C --> C2[变化多样]
        D --> D1[表达新颖]
        D --> D2[角度独特]
        E --> E1[传播能力]
        E --> E2[影响范围]
    ```
    
    ### 情感价值判断法
    ```mermaid
    flowchart LR
        A[价值判断] --> B[积极价值]
        A --> C[教育价值]
        A --> D[艺术价值]
        A --> E[社会价值]
        
        B --> B1[正面情感]
        B --> B2[健康导向]
        C --> C1[启发作用]
        C --> C2[成长帮助]
        D --> D1[美学价值]
        D --> D2[创新价值]
        E --> E1[社会意义]
        E --> E2[文化价值]
    ```
  </process>

  <criteria>
    ## 情感评估质量标准
    
    ### 体验真实性标准
    - ✅ 基于真实的情感体验和感受
    - ✅ 避免理性分析对情感体验的干扰
    - ✅ 保持情感反应的自然性和真实性
    - ✅ 记录完整的情感体验过程
    
    ### 评估准确性标准
    - ✅ 准确识别和分类各种情感类型
    - ✅ 准确评估情感的强度和质量
    - ✅ 准确判断情感的真实性和感染力
    - ✅ 准确预测情感的共鸣效果
    
    ### 分析客观性标准
    - ✅ 平衡主观体验与客观分析
    - ✅ 避免个人偏好的过度影响
    - ✅ 采用相对统一的评估标准
    - ✅ 提供可验证的评估依据
    
    ### 建议实用性标准
    - ✅ 提供具体的情感优化建议
    - ✅ 建议具有明确的针对性和可操作性
    - ✅ 考虑建议的实施可行性
    - ✅ 预期改进效果明确可测
    
    ### 价值导向性标准
    - ✅ 体现积极健康的情感价值观
    - ✅ 有助于读者的情感成长和发展
    - ✅ 促进优秀情感文化的传承
    - ✅ 具有正面的社会影响和教育意义
  </criteria>
</execution>
